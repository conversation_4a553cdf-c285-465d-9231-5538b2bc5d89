-- Create database if it doesn't exist
DO
$$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'geotabadapterdb') THEN
      CREATE DATABASE geotabadapterdb;
   END IF;
END
$$;

-- Create users if they don't exist
DO
$$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'pgadmin') THEN
      CREATE USER pgadmin WITH PASSWORD 'localdev123';
   END IF;
   IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'geotabadapter_client') THEN
      CREATE USER geotabadapter_client WITH PASSWORD 'localdev123';
   END IF;
END
$$;

-- Grant privileges
GRANT CONNECT ON DATABASE geotabadapterdb TO pgadmin, geotabadapter_client;

\c geotabadapterdb

GRANT USAGE ON SCHEMA public TO pgadmin, geotabadapter_client;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO pgadmin;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO geotabadapter_client;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO geotabadapter_client;

-- Make future tables/sequences accessible
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO pgadmin;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO geotabadapter_client;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO geotabadapter_client;