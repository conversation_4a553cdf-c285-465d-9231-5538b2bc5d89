{"mcpServers": {"grafana": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GRAFANA_URL", "-e", "GRAFANA_API_KEY", "mcp/grafana", "--transport=stdio"], "env": {"GRAFANA_URL": "http://host.docker.internal:3000", "GRAFANA_API_KEY": "glsa_G5pVxJ2w9mkneHZLmITfuPOMxwLXaNtL_010878a2"}, "alwaysAllow": ["list_datasources", "get_datasource_by_uid", "query_loki_logs", "update_dashboard", "get_dashboard_by_uid", "search_dashboards", "get_dashboard_panel_queries"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAUL9rByn8XfKJEaxMuHsB3lkSX8jF"}, "alwaysAllow": ["brave_web_search"]}, "Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "alwaysAllow": ["browser_tab_list"]}}}