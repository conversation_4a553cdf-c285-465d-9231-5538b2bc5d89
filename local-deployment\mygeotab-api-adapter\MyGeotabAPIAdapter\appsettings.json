{
  "OverrideSetings": {
    "DisableMachineNameValidation": false
  },
  "DatabaseSettings": {
    "UseDataModel2": false,
    "EnableLevel1DatabaseMaintenance": true,
    "Level1DatabaseMaintenanceIntervalMinutes": 30,
    "EnableLevel2DatabaseMaintenance": true,
    "Level2DatabaseMaintenanceIntervalMinutes": 60,
    "EnableLevel2DatabaseMaintenanceWindow": false,
    "Level2DatabaseMaintenanceWindowStartTimeUTC": "2020-06-23T06:00:00Z",
    "Level2DatabaseMaintenanceWindowMaxMinutes": 60,
    //"DatabaseProviderType": "PostgreSQL",
    //"DatabaseConnectionString": "Server=<Server>;Port=<Port>;Database=geotabadapterdb;User Id=geotabadapter_client;Password=<password>"
    "DatabaseProviderType": "SQLServer",
    "DatabaseConnectionString": "Server=<Server>;Database=geotabadapterdb;User Id=geotabadapter_client;Password=<password>;MultipleActiveResultSets=True;TrustServerCertificate=True"
    //"DatabaseProviderType": "Oracle",
    //"DatabaseConnectionString": "Data Source=<DataSource>;User Id=GeotabAdapter_Client;Password=<password>"
  },
  "LoginSettings": {
    "MyGeotabServer": "<MyGeotabServer>",
    "MyGeotabDatabase": "<MyGeotabDatabase>",
    "MyGeotabUser": "<MyGeotabUser>",
    "MyGeotabPassword": "<MyGeotabPassword>"
  },
  "AppSettings": {
    "GeneralSettings": {
      "TimeoutSecondsForDatabaseTasks": 600,
      "TimeoutSecondsForMyGeotabTasks": 3600
    },
    "Caches": {
      "Controller": {
        "EnableControllerCache": true,
        "ControllerCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "ControllerCacheUpdateIntervalMinutes": 10,
        "ControllerCacheRefreshIntervalMinutes": 10080
      },
      "Device": {
        "EnableDeviceCache": true,
        "DeviceCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "DeviceCacheUpdateIntervalMinutes": 1,
        "DeviceCacheRefreshIntervalMinutes": 1440
      },
      "Diagnostic": {
        "EnableDiagnosticCache": true,
        "DiagnosticCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "DiagnosticCacheUpdateIntervalMinutes": 10,
        "DiagnosticCacheRefreshIntervalMinutes": 1440
      },
      "DVIRDefect": {
        "DVIRDefectListCacheRefreshIntervalMinutes": 1440
      },
      "FailureMode": {
        "EnableFailureModeCache": true,
        "FailureModeCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "FailureModeCacheUpdateIntervalMinutes": 10,
        "FailureModeCacheRefreshIntervalMinutes": 1440
      },
      "Group": {
        "EnableGroupCache": true,
        "GroupCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "GroupCacheUpdateIntervalMinutes": 10,
        "GroupCacheRefreshIntervalMinutes": 1440
      },
      "Rule": {
        "EnableRuleCache": true,
        "RuleCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "RuleCacheUpdateIntervalMinutes": 10,
        "RuleCacheRefreshIntervalMinutes": 1440
      },
      "UnitOfMeasure": {
        "EnableUnitOfMeasureCache": true,
        "UnitOfMeasureCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "UnitOfMeasureCacheUpdateIntervalMinutes": 30,
        "UnitOfMeasureCacheRefreshIntervalMinutes": 10080
      },
      "User": {
        "EnableUserCache": true,
        "UserCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "UserCacheUpdateIntervalMinutes": 1,
        "UserCacheRefreshIntervalMinutes": 1440
      },
      "Zone": {
        "EnableZoneCache": true,
        "ZoneCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "ZoneCacheUpdateIntervalMinutes": 10,
        "ZoneCacheRefreshIntervalMinutes": 1440
      },
      "ZoneType": {
        "EnableZoneTypeCache": true,
        "ZoneTypeCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "ZoneTypeCacheUpdateIntervalMinutes": 30,
        "ZoneTypeCacheRefreshIntervalMinutes": 10080
      }
    },
    "GeneralFeedSettings": {
      "FeedStartOption": "CurrentTime",
      "FeedStartSpecificTimeUTC": "2020-06-23T06:00:00Z",
      "DevicesToTrack": "*",
      "DiagnosticsToTrack": "*",
      "ExcludeDiagnosticsToTrack": false,
      "EnableMinimunIntervalSamplingForLogRecords": false,
      "EnableMinimunIntervalSamplingForStatusData": false,
      "MinimumIntervalSamplingDiagnostics": "*",
      "MinimumIntervalSamplingIntervalSeconds": 300
    },
    "Feeds": {
      "BinaryData": {
        "EnableBinaryDataFeed": false,
        "BinaryDataFeedIntervalSeconds": 10
      },
      "ChargeEvent": {
        "EnableChargeEventFeed": false,
        "ChargeEventFeedIntervalSeconds": 10
      },
      "DebugData": {
        "EnableDebugDataFeed": false,
        "DebugDataFeedIntervalSeconds": 10
      },
      "DeviceStatusInfo": {
        "EnableDeviceStatusInfoFeed": false,
        "DeviceStatusInfoFeedIntervalSeconds": 10
      },
      "DriverChange": {
        "EnableDriverChangeFeed": false,
        "DriverChangeFeedIntervalSeconds": 10
      },
      "DutyStatusAvailability": {
        "EnableDutyStatusAvailabilityFeed": false,
        "DutyStatusAvailabilityFeedIntervalSeconds": 300,
        "DutyStatusAvailabilityFeedLastAccessDateCutoffDays": 30
      },
      "DutyStatusLog": {
        "EnableDutyStatusLogFeed": false,
        "DutyStatusLogFeedIntervalSeconds": 10
      },
      "DVIRLog": {
        "EnableDVIRLogFeed": false,
        "DVIRLogFeedIntervalSeconds": 10
      },
      "ExceptionEvent": {
        "EnableExceptionEventFeed": false,
        "ExceptionEventFeedIntervalSeconds": 10,
        "TrackZoneStops": true
      },
      "FaultData": {
        "EnableFaultDataFeed": false,
        "FaultDataFeedIntervalSeconds": 10
      },
      "LogRecord": {
        "EnableLogRecordFeed": false,
        "LogRecordFeedIntervalSeconds": 10
      },
      "StatusData": {
        "EnableStatusDataFeed": false,
        "StatusDataFeedIntervalSeconds": 10
      },
      "Trip": {
        "EnableTripFeed": false,
        "TripFeedIntervalSeconds": 10
      }
    },
    "DataEnhancementServices": {
      "FaultData": {
        "EnableFaultDataLocationService": false,
        "FaultDataLocationServiceOperationMode": "Continuous",
        "FaultDataLocationServiceDailyStartTimeUTC": "2020-06-23T06:00:00Z",
        "FaultDataLocationServiceDailyRunTimeSeconds": 21600,
        "FaultDataLocationServiceExecutionIntervalSeconds": 10,
        "FaultDataLocationServicePopulateSpeed": true,
        "FaultDataLocationServicePopulateBearing": true,
        "FaultDataLocationServicePopulateDirection": true,
        "FaultDataLocationServiceNumberOfCompassDirections": 16,
        "FaultDataLocationServiceMaxDaysPerBatch": 2,
        "FaultDataLocationServiceMaxBatchSize": 100000,
        "FaultDataLocationServiceBufferMinutes": 1440
      },
      "StatusData": {
        "EnableStatusDataLocationService": false,
        "StatusDataLocationServiceOperationMode": "Continuous",
        "StatusDataLocationServiceDailyStartTimeUTC": "2020-06-23T06:00:00Z",
        "StatusDataLocationServiceDailyRunTimeSeconds": 21600,
        "StatusDataLocationServiceExecutionIntervalSeconds": 10,
        "StatusDataLocationServicePopulateSpeed": true,
        "StatusDataLocationServicePopulateBearing": true,
        "StatusDataLocationServicePopulateDirection": true,
        "StatusDataLocationServiceNumberOfCompassDirections": 16,
        "StatusDataLocationServiceMaxDaysPerBatch": 2,
        "StatusDataLocationServiceMaxBatchSize": 100000,
        "StatusDataLocationServiceBufferMinutes": 1440
      }
    },
    "Manipulators": {
      "DVIRLog": {
        "EnableDVIRLogManipulator": false,
        "DVIRLogManipulatorIntervalSeconds": 2
      }
    },
    "AddOns": {
      "VSS": {
        "EnableVSSAddOn": false,
        "DisableCorrespondingAdapterOutput": false,
        "OutputLogRecordsToOVDS": true,
        "OutputStatusDataToOVDS": true,
        "LogUnmappedDiagnostics": false,
        "UnmappedDiagnosticsLogIntervalMinutes": 5,
        "VSSPathMapFileURL": "https://storage.googleapis.com/geotab-vehicle_signal_specification-vsspathmaps/VSSPathMaps.json",
        "VSSVersion": "2.3",
        "VSSPathMapUpdateIntervalMinutes": 1440,
        "SendAttributeTypeDataToOVDS": true,
        "OVDSClientWorkerIntervalSeconds": 30,
        "OVDSServerURL": "http://*************:8765/ovdsserver",
        "OVDSSetCommandTemplate": "{\"action\":\"set\",\"vin\":\"PLACEHOLDER_VIN\",\"path\":\"PLACEHOLDER_PATH\",\"value\":\"PLACEHOLDER_VALUE\",\"timestamp\":\"PLACEHOLDER_TIMESTAMP\"}",
        "OVDSSetCommandTemplateForAttributeVSSPathMaps": "{\"action\":\"set\",\"vin\":\"PLACEHOLDER_VIN\",\"path\":\"PLACEHOLDER_PATH\",\"value\":\"PLACEHOLDER_VALUE\"}"
      }
    }
  }
}
