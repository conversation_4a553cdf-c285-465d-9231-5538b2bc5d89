{"name": "mygeotab-local-deployment", "version": "1.0.0", "description": "Local deployment for MyGeotab Fleet Monitoring using Official Geotab API Adapter", "scripts": {"configure-mygeotab": "node configure-mygeotab.js", "test-adapter": "node test-adapter.js", "start-adapter": "start-adapter.bat", "start-adapter-unix": "bash start-adapter.sh", "logs": "docker-compose logs -f", "up": "docker-compose up -d", "down": "docker-compose down", "reset": "docker-compose down -v && docker-compose up -d"}, "dependencies": {"pg": "^8.16.1"}, "license": "MIT"}