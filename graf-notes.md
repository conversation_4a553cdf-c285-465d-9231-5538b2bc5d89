# Grafana Vehicle Data Reference Guide

This document provides comprehensive information on accessing vehicle data from the MyGeotab API Adapter database for use in Grafana dashboards.

## Table of Contents
1. [Odometer Data Access](#odometer-data-access)
2. [Vehicle Identification Data](#vehicle-identification-data)
3. [Vehicle Specifications](#vehicle-specifications)
4. [Current Vehicle Status](#current-vehicle-status)
5. [Diagnostic Parameters](#diagnostic-parameters)
6. [Trip Data](#trip-data)
7. [Location & Operational Data](#location--operational-data)
8. [Driver Information](#driver-information)
9. [Common Query Patterns](#common-query-patterns)

---

## Odometer Data Access

### Primary Source: DVIRLogs2 Table
**Table:** `DVIRLogs2`  
**Column:** `Odometer` (double precision)  
**Description:** Direct odometer readings from Driver Vehicle Inspection Reports

```sql
-- Get latest odometer reading per vehicle
SELECT 
  d."Name" as "Vehicle",
  dvir."Odometer" as "Odometer (km)",
  dvir."DateTime" as "Last Reading"
FROM "Devices2" d
LEFT JOIN (
  SELECT DISTINCT ON ("DeviceId") 
    "DeviceId", "Odometer", "DateTime"
  FROM "DVIRLogs2"
  WHERE "Odometer" IS NOT NULL
  ORDER BY "DeviceId", "DateTime" DESC
) dvir ON d.id = dvir."DeviceId"
ORDER BY d."Name"
```

### Secondary Source: StatusData2 with Diagnostics
**Tables:** `StatusData2` + `Diagnostics2`  
**Description:** Odometer data from diagnostic parameters

```sql
-- Get odometer data from StatusData2
SELECT 
  d."Name" as "Vehicle",
  sd."Data" as "Odometer (km)",
  sd."DateTime" as "Reading Time"
FROM "Devices2" d
JOIN "StatusData2" sd ON d.id = sd."DeviceId"
JOIN "Diagnostics2" diag ON sd."DiagnosticId" = diag.id
WHERE diag."DiagnosticName" LIKE '%Odometer%' 
   OR diag."DiagnosticName" LIKE '%Distance%'
   OR diag."DiagnosticUnitOfMeasureName" LIKE '%km%'
ORDER BY sd."DateTime" DESC
```

### Odometer Trends Over Time
```sql
-- Track odometer changes over time (for Time Series panels)
SELECT 
  $__time("DateTime"),
  d."Name" as metric,
  "Odometer" as value
FROM "DVIRLogs2" dvir
JOIN "Devices2" d ON dvir."DeviceId" = d.id
WHERE $__timeFilter("DateTime")
  AND "Odometer" IS NOT NULL
ORDER BY "DateTime"
```

---

## Vehicle Identification Data

### Devices2 Table Schema
**Table:** `Devices2`

| Column | Data Type | Description |
|--------|-----------|-------------|
| `id` | bigint | Primary key |
| `GeotabId` | varchar(50) | Geotab unique identifier |
| `Name` | varchar(100) | Vehicle display name |
| `VIN` | varchar(50) | Vehicle Identification Number |
| `LicensePlate` | varchar(50) | License plate number |
| `LicenseState` | varchar(50) | License plate state/province |
| `SerialNumber` | varchar(12) | Device serial number |
| `DeviceType` | varchar(50) | Type of Geotab device |
| `Comment` | varchar(1024) | Custom comments/notes |
| `Groups` | text | JSON array of group assignments |

### Vehicle Identification Queries

```sql
-- Complete vehicle identification information
SELECT 
  "Name" as "Vehicle Name",
  "VIN" as "VIN Number",
  "LicensePlate" as "License Plate",
  "LicenseState" as "State/Province",
  "SerialNumber" as "Device Serial",
  "DeviceType" as "Device Type",
  "Comment" as "Notes"
FROM "Devices2"
WHERE "EntityStatus" = 1  -- Active vehicles only
ORDER BY "Name"
```

```sql
-- Search vehicles by VIN or License Plate
SELECT 
  "Name", "VIN", "LicensePlate", "LicenseState"
FROM "Devices2"
WHERE "VIN" ILIKE '%${vin_search}%' 
   OR "LicensePlate" ILIKE '%${plate_search}%'
ORDER BY "Name"
```

---

## Vehicle Specifications

### Custom Fields and Groups
**Column:** `Groups` (text - JSON format)  
**Column:** `Comment` (varchar(1024))

```sql
-- Extract vehicle groups (requires JSON parsing)
SELECT 
  "Name" as "Vehicle",
  "Groups" as "Group Assignments",
  "Comment" as "Custom Notes"
FROM "Devices2"
WHERE "Groups" IS NOT NULL
ORDER BY "Name"
```

### Product Information
**Column:** `ProductId` (integer)

```sql
-- Vehicle product information
SELECT 
  "Name" as "Vehicle",
  "ProductId" as "Product ID",
  "DeviceType" as "Device Type",
  "SerialNumber" as "Serial Number"
FROM "Devices2"
ORDER BY "Name"
```

---

## Current Vehicle Status

### DeviceStatusInfo2 Table Schema
**Table:** `DeviceStatusInfo2` (Real-time vehicle status)

| Column | Data Type | Description |
|--------|-----------|-------------|
| `DeviceId` | bigint | Reference to Devices2 |
| `DriverId` | bigint | Currently assigned driver |
| `DateTime` | timestamp | Last status update |
| `Latitude` | double | Current latitude |
| `Longitude` | double | Current longitude |
| `Speed` | real | Current speed |
| `Bearing` | double | Direction of travel |
| `IsDeviceCommunicating` | boolean | Device connectivity status |
| `IsDriving` | boolean | Currently driving |
| `CurrentStateDuration` | varchar(50) | Duration in current state |

### Current Status Queries

```sql
-- Real-time vehicle status dashboard
SELECT 
  d."Name" as "Vehicle",
  dsi."Speed" as "Current Speed (km/h)",
  CASE 
    WHEN dsi."IsDeviceCommunicating" = false THEN 'Offline'
    WHEN dsi."IsDriving" = true THEN 'Driving'
    WHEN dsi."Speed" > 5 THEN 'Moving'
    WHEN dsi."Speed" > 0 THEN 'Idling'
    ELSE 'Stopped'
  END as "Status",
  dsi."CurrentStateDuration" as "State Duration",
  dsi."DateTime" as "Last Update",
  u."Name" as "Current Driver"
FROM "Devices2" d
LEFT JOIN "DeviceStatusInfo2" dsi ON d.id = dsi."DeviceId"
LEFT JOIN "Users2" u ON dsi."DriverId" = u.id
ORDER BY d."Name"
```

```sql
-- Vehicle location map data
SELECT 
  d."Name" as "Vehicle",
  dsi."Latitude" as "lat",
  dsi."Longitude" as "lng",
  dsi."Speed" as "Speed",
  dsi."Bearing" as "Heading"
FROM "Devices2" d
JOIN "DeviceStatusInfo2" dsi ON d.id = dsi."DeviceId"
WHERE dsi."Latitude" IS NOT NULL 
  AND dsi."Longitude" IS NOT NULL
```

---

## Diagnostic Parameters

### StatusData2 + Diagnostics2 Tables
**Description:** Access to all vehicle diagnostic parameters including fluid levels, engine hours, etc.

### Common Diagnostic Categories

```sql
-- Find available diagnostic parameters
SELECT DISTINCT
  "DiagnosticName",
  "DiagnosticUnitOfMeasureName",
  "DiagnosticSourceName",
  COUNT(*) as "Record Count"
FROM "Diagnostics2" d
JOIN "StatusData2" sd ON d.id = sd."DiagnosticId"
WHERE d."EntityStatus" = 1
GROUP BY "DiagnosticName", "DiagnosticUnitOfMeasureName", "DiagnosticSourceName"
ORDER BY "DiagnosticName"
```

### Fluid Levels Query Pattern
```sql
-- Generic fluid levels query (DEF, Fuel, Oil, etc.)
SELECT 
  d."Name" as "Vehicle",
  diag."DiagnosticName" as "Parameter",
  sd."Data" as "Value",
  diag."DiagnosticUnitOfMeasureName" as "Unit",
  sd."DateTime" as "Reading Time"
FROM "Devices2" d
JOIN "StatusData2" sd ON d.id = sd."DeviceId"
JOIN "Diagnostics2" diag ON sd."DiagnosticId" = diag.id
WHERE diag."DiagnosticName" ILIKE '%fuel%'
   OR diag."DiagnosticName" ILIKE '%def%'
   OR diag."DiagnosticName" ILIKE '%oil%'
   OR diag."DiagnosticName" ILIKE '%coolant%'
ORDER BY sd."DateTime" DESC
```

### Engine Hours Query
```sql
-- Engine hours tracking
SELECT 
  d."Name" as "Vehicle",
  sd."Data" as "Engine Hours",
  sd."DateTime" as "Reading Time"
FROM "Devices2" d
JOIN "StatusData2" sd ON d.id = sd."DeviceId"
JOIN "Diagnostics2" diag ON sd."DiagnosticId" = diag.id
WHERE diag."DiagnosticName" ILIKE '%engine%hour%'
   OR diag."DiagnosticName" ILIKE '%total%engine%time%'
ORDER BY sd."DateTime" DESC
```

---

## Trip Data

### Trips2 Table Schema
**Table:** `Trips2`

| Column | Data Type | Description |
|--------|-----------|-------------|
| `DeviceId` | bigint | Vehicle reference |
| `DriverId` | bigint | Driver reference |
| `Start` | timestamp | Trip start time |
| `Stop` | timestamp | Trip end time |
| `Distance` | real | Trip distance |
| `AverageSpeed` | real | Average speed |
| `MaximumSpeed` | real | Maximum speed |
| `DrivingDurationTicks` | bigint | Driving time (ticks) |
| `IdlingDurationTicks` | bigint | Idling time (ticks) |
| `StopDurationTicks` | bigint | Stop time (ticks) |

### Trip Analysis Queries

```sql
-- Daily trip summary
SELECT 
  d."Name" as "Vehicle",
  DATE(t."Start") as "Date",
  COUNT(*) as "Trip Count",
  SUM(t."Distance") as "Total Distance (km)",
  AVG(t."AverageSpeed") as "Avg Speed (km/h)",
  SUM(t."DrivingDurationTicks") / 10000000.0 / 3600 as "Driving Hours"
FROM "Trips2" t
JOIN "Devices2" d ON t."DeviceId" = d.id
WHERE t."Start" >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY d."Name", DATE(t."Start")
ORDER BY "Date" DESC, d."Name"
```

---

## Location & Operational Data

### LogRecords2 Table (GPS Tracking)
**Table:** `LogRecords2`

```sql
-- Recent GPS positions
SELECT 
  d."Name" as "Vehicle",
  lr."DateTime" as "Timestamp",
  lr."Latitude",
  lr."Longitude", 
  lr."Speed" as "Speed (km/h)"
FROM "LogRecords2" lr
JOIN "Devices2" d ON lr."DeviceId" = d.id
WHERE lr."DateTime" >= NOW() - INTERVAL '1 hour'
ORDER BY lr."DateTime" DESC
```

---

## Driver Information

### Users2 Table Schema
**Table:** `Users2`

```sql
-- Driver assignments and information
SELECT 
  d."Name" as "Vehicle",
  u."Name" as "Driver Name",
  u."FirstName",
  u."LastName",
  u."EmployeeNo" as "Employee Number",
  dsi."DateTime" as "Assignment Time"
FROM "DeviceStatusInfo2" dsi
JOIN "Devices2" d ON dsi."DeviceId" = d.id
JOIN "Users2" u ON dsi."DriverId" = u.id
WHERE u."IsDriver" = true
ORDER BY d."Name"
```

---

## Common Query Patterns

### Time-based Filtering
```sql
-- Use Grafana time range variables
WHERE $__timeFilter("DateTime")
-- Or specific intervals
WHERE "DateTime" >= NOW() - INTERVAL '24 hours'
```

### Vehicle Filtering
```sql
-- Template variable for vehicle selection
WHERE d."Name" = '${vehicle_name}'
-- Or multiple vehicles
WHERE d."Name" IN (${vehicle_names:sqlstring})
```

### Data Visualization Tips
- **Table panels:** Use for current status and identification data
- **Time series:** Use for trends (odometer, fuel, engine hours)
- **Stat panels:** Use for current values and KPIs
- **Geomap panels:** Use for location data with lat/lng columns
- **Bar charts:** Use for comparisons between vehicles

---

## Fault Data & Maintenance Indicators

### FaultData2 Table Schema
**Table:** `FaultData2` (Vehicle fault codes and diagnostic trouble codes)

| Column | Data Type | Description |
|--------|-----------|-------------|
| `DeviceId` | bigint | Vehicle reference |
| `DiagnosticId` | bigint | Diagnostic parameter reference |
| `DateTime` | timestamp | Fault occurrence time |
| `Count` | integer | Fault occurrence count |
| `ControllerId` | varchar(100) | ECU controller ID |
| `ControllerName` | varchar(255) | ECU controller name |
| `FailureModeName` | varchar(255) | Fault description |
| `Severity` | varchar(50) | Fault severity level |
| `FaultState` | varchar(50) | Current fault state |
| `MalfunctionLamp` | boolean | Check engine light status |
| `AmberWarningLamp` | boolean | Amber warning light |
| `RedStopLamp` | boolean | Red stop light |
| `DismissDateTime` | timestamp | When fault was dismissed |

### Active Faults Query

```sql
-- Current active faults by vehicle
SELECT
  d."Name" as "Vehicle",
  f."ControllerName" as "System",
  f."FailureModeName" as "Fault Description",
  f."Severity" as "Severity",
  f."Count" as "Occurrence Count",
  f."DateTime" as "First Occurred",
  CASE
    WHEN f."MalfunctionLamp" = true THEN 'Check Engine'
    WHEN f."RedStopLamp" = true THEN 'Stop Required'
    WHEN f."AmberWarningLamp" = true THEN 'Warning'
    ELSE 'Information'
  END as "Lamp Status"
FROM "FaultData2" f
JOIN "Devices2" d ON f."DeviceId" = d.id
WHERE f."DismissDateTime" IS NULL  -- Active faults only
  AND f."FaultState" != 'Inactive'
ORDER BY f."DateTime" DESC
```

### Fault Trends and Statistics

```sql
-- Fault frequency by vehicle (last 30 days)
SELECT
  d."Name" as "Vehicle",
  COUNT(*) as "Total Faults",
  COUNT(CASE WHEN f."Severity" = 'Critical' THEN 1 END) as "Critical Faults",
  COUNT(CASE WHEN f."MalfunctionLamp" = true THEN 1 END) as "Check Engine Events",
  MAX(f."DateTime") as "Last Fault"
FROM "FaultData2" f
JOIN "Devices2" d ON f."DeviceId" = d.id
WHERE f."DateTime" >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY d."Name"
ORDER BY "Total Faults" DESC
```

---

## DVIR (Driver Vehicle Inspection Report) Data

### DVIRLogs2 Table Extended Schema
**Table:** `DVIRLogs2`

| Column | Data Type | Description |
|--------|-----------|-------------|
| `DeviceId` | bigint | Vehicle reference |
| `DriverId` | bigint | Inspecting driver |
| `DateTime` | timestamp | Inspection time |
| `Odometer` | double | Odometer reading |
| `EngineHours` | real | Engine hours at inspection |
| `IsSafeToOperate` | boolean | Vehicle safety status |
| `LogType` | varchar(50) | Type of inspection |
| `LocationLatitude` | double | Inspection location |
| `LocationLongitude` | double | Inspection location |
| `DriverRemark` | text | Driver comments |
| `RepairDate` | timestamp | When repairs completed |
| `RepairedByUserId` | bigint | Who performed repairs |

### DVIR Analysis Queries

```sql
-- Vehicle inspection compliance
SELECT
  d."Name" as "Vehicle",
  COUNT(*) as "Inspections This Month",
  COUNT(CASE WHEN dvir."IsSafeToOperate" = false THEN 1 END) as "Failed Inspections",
  MAX(dvir."DateTime") as "Last Inspection",
  AVG(dvir."Odometer") as "Avg Odometer Reading"
FROM "DVIRLogs2" dvir
JOIN "Devices2" d ON dvir."DeviceId" = d.id
WHERE dvir."DateTime" >= DATE_TRUNC('month', CURRENT_DATE)
GROUP BY d."Name"
ORDER BY "Last Inspection" DESC
```

```sql
-- Vehicles requiring attention
SELECT
  d."Name" as "Vehicle",
  dvir."DateTime" as "Inspection Date",
  dvir."IsSafeToOperate" as "Safe to Operate",
  dvir."DriverRemark" as "Issues Found",
  CASE
    WHEN dvir."RepairDate" IS NOT NULL THEN 'Repaired'
    WHEN dvir."IsSafeToOperate" = false THEN 'Needs Repair'
    ELSE 'OK'
  END as "Status"
FROM "DVIRLogs2" dvir
JOIN "Devices2" d ON dvir."DeviceId" = d.id
WHERE dvir."IsSafeToOperate" = false
  AND dvir."RepairDate" IS NULL
ORDER BY dvir."DateTime" DESC
```

---

## Advanced Diagnostic Queries

### Fuel Efficiency Analysis
```sql
-- Fuel consumption trends
SELECT
  d."Name" as "Vehicle",
  DATE(sd."DateTime") as "Date",
  AVG(sd."Data") as "Avg Fuel Level (%)",
  MIN(sd."Data") as "Min Fuel Level (%)",
  MAX(sd."Data") as "Max Fuel Level (%)"
FROM "StatusData2" sd
JOIN "Devices2" d ON sd."DeviceId" = d.id
JOIN "Diagnostics2" diag ON sd."DiagnosticId" = diag.id
WHERE diag."DiagnosticName" ILIKE '%fuel%level%'
  AND sd."DateTime" >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY d."Name", DATE(sd."DateTime")
ORDER BY "Date" DESC, d."Name"
```

### DEF (Diesel Exhaust Fluid) Monitoring
```sql
-- DEF level alerts
SELECT
  d."Name" as "Vehicle",
  sd."Data" as "DEF Level (%)",
  sd."DateTime" as "Reading Time",
  CASE
    WHEN sd."Data" < 5 THEN 'Critical - Refill Immediately'
    WHEN sd."Data" < 15 THEN 'Low - Schedule Refill'
    WHEN sd."Data" < 30 THEN 'Monitor'
    ELSE 'OK'
  END as "Status"
FROM "StatusData2" sd
JOIN "Devices2" d ON sd."DeviceId" = d.id
JOIN "Diagnostics2" diag ON sd."DiagnosticId" = diag.id
WHERE diag."DiagnosticName" ILIKE '%def%'
   OR diag."DiagnosticName" ILIKE '%diesel%exhaust%fluid%'
   OR diag."DiagnosticName" ILIKE '%adblue%'
ORDER BY sd."Data" ASC, sd."DateTime" DESC
```

### Engine Performance Metrics
```sql
-- Engine health indicators
SELECT
  d."Name" as "Vehicle",
  diag."DiagnosticName" as "Parameter",
  sd."Data" as "Value",
  diag."DiagnosticUnitOfMeasureName" as "Unit",
  sd."DateTime" as "Reading Time"
FROM "StatusData2" sd
JOIN "Devices2" d ON sd."DeviceId" = d.id
JOIN "Diagnostics2" diag ON sd."DiagnosticId" = diag.id
WHERE (diag."DiagnosticName" ILIKE '%engine%temp%'
   OR diag."DiagnosticName" ILIKE '%oil%pressure%'
   OR diag."DiagnosticName" ILIKE '%coolant%temp%'
   OR diag."DiagnosticName" ILIKE '%turbo%pressure%')
  AND sd."DateTime" >= NOW() - INTERVAL '1 hour'
ORDER BY d."Name", diag."DiagnosticName"
```

---

## Geofencing and Zone Data

### Zones2 Table Schema
**Table:** `Zones2` (Geofence definitions)

| Column | Data Type | Description |
|--------|-----------|-------------|
| `Name` | varchar(255) | Zone name |
| `CentroidLatitude` | double | Zone center latitude |
| `CentroidLongitude` | double | Zone center longitude |
| `Points` | text | Zone boundary coordinates |
| `Comment` | varchar(500) | Zone description |
| `ActiveFrom` | timestamp | Zone active start |
| `ActiveTo` | timestamp | Zone active end |

### Zone Analysis Queries
```sql
-- List all active geofences
SELECT
  "Name" as "Zone Name",
  "Comment" as "Description",
  "CentroidLatitude" as "Center Lat",
  "CentroidLongitude" as "Center Lng",
  "ActiveFrom" as "Active From",
  "ActiveTo" as "Active To"
FROM "Zones2"
WHERE "EntityStatus" = 1
  AND ("ActiveTo" IS NULL OR "ActiveTo" > NOW())
ORDER BY "Name"
```

---

## Performance Optimization Tips

### Indexing Considerations
- Always filter by `DateTime` ranges for time-series data
- Use `DeviceId` filters when querying specific vehicles
- Consider using `DISTINCT ON` for latest record queries

### Query Performance Best Practices
```sql
-- Efficient latest status query pattern
SELECT DISTINCT ON (d.id)
  d."Name",
  lr."DateTime",
  lr."Speed"
FROM "Devices2" d
LEFT JOIN "LogRecords2" lr ON d.id = lr."DeviceId"
WHERE lr."DateTime" >= NOW() - INTERVAL '1 hour'
ORDER BY d.id, lr."DateTime" DESC
```

### Grafana Variable Examples
```sql
-- Vehicle selection variable
SELECT "Name" as __text, "Name" as __value
FROM "Devices2"
WHERE "EntityStatus" = 1
ORDER BY "Name"

-- Diagnostic parameter selection
SELECT DISTINCT "DiagnosticName" as __text, "DiagnosticName" as __value
FROM "Diagnostics2"
WHERE "EntityStatus" = 1
ORDER BY "DiagnosticName"
```

---

## Dashboard Design Recommendations

### Real-Time Operations Dashboard
- Vehicle status table with current location, speed, driver
- Map panel showing vehicle positions
- Alert panels for active faults and low fluid levels
- KPI stats for fleet availability and utilization

### Maintenance Dashboard
- DVIR compliance tracking
- Fault frequency trends
- Fluid level monitoring
- Engine hours and odometer progression

### Fleet Analytics Dashboard
- Trip analysis and fuel efficiency
- Driver performance metrics
- Utilization reports by vehicle/driver
- Cost analysis and trends

### Alert Configuration
- Set up Grafana alerts for:
  - Vehicles offline for extended periods
  - Critical fault codes
  - Low DEF/fuel levels
  - Overdue inspections
  - Geofence violations

