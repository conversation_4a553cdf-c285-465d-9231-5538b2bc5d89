# 🚀 MyGeotab Fleet Monitoring - Quick Start

Get your MyGeotab fleet monitoring system running in **5 minutes** using the Official Geotab MyGeotab API Adapter.

## ⚡ Prerequisites

- **Windows environment** (Official adapter is Windows-specific)
- **Docker Desktop** installed and running
- **MyGeotab account** with API access
- **Available ports**: 3000, 4000, 5432, 6379, 8080, 8081

## 🎯 Step 1: Deploy Infrastructure (2 minutes)

```bash
# Clone or download this repository
cd mygeotab-fleet-monitoring

# Start Docker infrastructure
cd local-deployment
docker-compose up -d
```

**Wait 2 minutes** for all services to start. You'll see:
- PostgreSQL database
- Grafana dashboards
- Redis cache
- PgHero database monitoring
- Dozzle log viewer

## 🔧 Step 2: Configure MyGeotab (1 minute)

```bash
# Run the configuration wizard
npm run configure-mygeotab
```

Enter your MyGeotab credentials:
- **Server**: `my.geotab.com` (or your custom server)
- **Database**: Your MyGeotab database name
- **Username**: Your MyGeotab username
- **Password**: Your MyGeotab password

## 🚛 Step 3: Download & Start Official Adapter (2 minutes)

### Download the Official Adapter
1. Go to [Geotab MyGeotab API Adapter Releases](https://github.com/Geotab/mygeotab-api-adapter/releases)
2. Download `MyGeotabAPIAdapter_SCD_win-x64.zip`
3. Extract to `local-deployment/official-adapter/`

### Start the Adapter
```bash
# Start the official MyGeotab API Adapter
npm run start-adapter
```

The adapter will:
- Connect to your MyGeotab database
- Create the complete official database schema
- Begin real-time data synchronization (every 30 seconds)

## ✅ Step 4: Verify Everything Works (1 minute)

```bash
# Test the setup
npm run test-adapter
```

**Check these URLs:**

| Service | URL | Purpose |
|---------|-----|---------|
| **Grafana** | http://localhost:3000 | Fleet dashboards (admin/admin) |
| **PgHero** | http://localhost:8081 | Database monitoring |
| **Dozzle** | http://localhost:8080 | Container logs |

## 📊 What You Get

### **Real-time Fleet Data**
- Vehicle locations and status
- Trip data and analytics
- Driver performance metrics
- Fault codes and diagnostics
- Engine health monitoring

### **Official Database Schema**
- **LogRecords2** - GPS tracking data
- **StatusData2** - Engine diagnostics
- **FaultData2** - Fault codes
- **Trips2** - Trip information
- **Devices2** - Vehicle information
- **Users2** - Driver information
- **Plus 20+ additional tables**

### **Monitoring Stack**
- Real-time Grafana dashboards
- PostgreSQL performance monitoring
- Container log management
- Redis caching for performance

## 🔍 Quick Verification

### Check Database Data
```bash
# Connect to database
docker exec postgres psql -U pgadmin -d monitoring_db

# Check vehicle count
SELECT COUNT(*) FROM "Devices2";

# Check latest GPS data
SELECT "DeviceId", "DateTime", "Latitude", "Longitude", "Speed"
FROM "LogRecords2"
ORDER BY "DateTime" DESC LIMIT 5;
```

### Check Services
```bash
# View all container status
docker-compose ps

# View logs
docker-compose logs -f
```

## 🛠️ Common Commands

```bash
# Stop all services
docker-compose down

# Reset everything (delete data)
docker-compose down -v
docker-compose up -d

# Reconfigure MyGeotab
npm run configure-mygeotab

# Test adapter connection
npm run test-adapter
```

## 🔧 Troubleshooting

### **No Data in Database?**
1. Wait 2-3 minutes for first sync
2. Check adapter is running and connected
3. Verify MyGeotab credentials are correct

### **Grafana Can't Connect?**
1. Use hostname `postgres` not `localhost`
2. Check credentials: pgadmin/localdev123
3. Verify PostgreSQL is running

### **MyGeotab Connection Issues?**
1. Check credentials at configuration wizard
2. Verify MyGeotab server URL
3. Ensure account has API access
4. Check Windows firewall settings

### **Reset Everything**
```bash
# Complete reset
docker-compose down -v
docker-compose up -d
npm run configure-mygeotab
npm run start-adapter
```

## 🎉 You're Ready!

Your MyGeotab fleet monitoring system is now running with:
- ✅ Official Geotab API Adapter
- ✅ Real-time data synchronization
- ✅ Complete database schema
- ✅ Grafana dashboards
- ✅ Performance monitoring

**Next Steps:**
1. Create custom Grafana dashboards for your fleet
2. Set up alerts for critical events
3. Explore the official database schema
4. Build custom reports and analytics

---

**🚛 Happy fleet monitoring!**

For detailed setup information, see [SETUP.md](SETUP.md)
