{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "postgres", "uid": "c8659a04-a35a-4a5d-8f30-9d8b1aa2dfc2"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.2.0", "targets": [{"format": "table", "rawSql": "SELECT * FROM \"Devices2\" LIMIT 50;", "refId": "A"}], "title": "Vehicle List", "type": "table"}], "refresh": "", "schemaVersion": 38, "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "All Vehicles", "uid": "f1bdc010-0a76-4560-a038-29a00bb42c3f", "version": 29, "weekStart": ""}