#!/usr/bin/env node

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function testAdapterSetup() {
    console.log('\n🔍 Testing MyGeotab Adapter Setup');
    console.log('='.repeat(40));

    // Test database connection
    const client = new Client({
        host: 'localhost',
        port: 5432,
        database: 'monitoring_db',
        user: 'pgadmin',
        password: 'localdev123'
    });

    try {
        await client.connect();
        console.log('✅ Database connection successful');

        // Check if official tables exist
        const tables = [
            'Devices2',
            'LogRecords2', 
            'StatusData2',
            'FaultData2',
            'Trips2',
            'Users2'
        ];

        console.log('\n📊 Checking Official Geotab Tables:');
        for (const table of tables) {
            try {
                const result = await client.query(`SELECT COUNT(*) FROM "${table}"`);
                console.log(`✅ ${table}: ${result.rows[0].count} records`);
            } catch (error) {
                console.log(`❌ ${table}: Table not found or error`);
            }
        }

        // Check configuration file
        console.log('\n⚙️  Checking Configuration:');
        const configPath = path.join(__dirname, 'official-adapter', 'MyGeotabAPIAdapter_SCD_win-x64', 'appsettings.json');
        
        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            
            console.log(`✅ Config file exists`);
            console.log(`📍 Database Provider: ${config.DatabaseSettings.DatabaseProviderType}`);
            console.log(`🔗 MyGeotab Server: ${config.LoginSettings.MyGeotabServer}`);
            console.log(`🏢 MyGeotab Database: ${config.LoginSettings.MyGeotabDatabase}`);
            
            // Check if credentials are configured
            if (config.LoginSettings.MyGeotabUser === 'PLACEHOLDER_USER') {
                console.log('⚠️  MyGeotab credentials not configured. Run: npm run configure-mygeotab');
            } else {
                console.log('✅ MyGeotab credentials configured');
            }

            // Check enabled feeds
            const feeds = config.AppSettings.Feeds;
            console.log('\n📡 Enabled Data Feeds:');
            Object.keys(feeds).forEach(feedName => {
                const feed = feeds[feedName];
                const enabledKey = Object.keys(feed).find(key => key.startsWith('Enable'));
                if (enabledKey && feed[enabledKey]) {
                    console.log(`✅ ${feedName}: ${feed[enabledKey] ? 'Enabled' : 'Disabled'}`);
                }
            });

        } else {
            console.log('❌ Configuration file not found');
        }

        // Check adapter executable
        console.log('\n🔧 Checking Adapter Files:');
        const adapterPath = path.join(__dirname, 'official-adapter', 'MyGeotabAPIAdapter_SCD_win-x64', 'MyGeotabAPIAdapter.exe');
        if (fs.existsSync(adapterPath)) {
            console.log('✅ MyGeotab Adapter executable found');
        } else {
            console.log('❌ MyGeotab Adapter executable not found');
        }

        console.log('\n🎯 Next Steps:');
        console.log('1. Configure MyGeotab credentials: npm run configure-mygeotab');
        console.log('2. Start the adapter: npm run start-adapter');
        console.log('3. Monitor in Grafana: http://localhost:3000');

    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        console.log('\n💡 Make sure PostgreSQL is running: docker-compose up postgres -d');
    } finally {
        await client.end();
    }
}

// Run the test
testAdapterSetup().catch(console.error);
