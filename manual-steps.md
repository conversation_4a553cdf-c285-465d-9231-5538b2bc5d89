# Local Deployment Setup Instructions

## 1. Initialize the Database

**You must run the database setup script before starting the adapter.**

From your project root, run:

```
local-deployment\setup-postgres-users.bat
```

This script will:
- Create required Postgres users and permissions
- Create or update the database schema
- Initialize all required partitions

> **Do not skip this step!**  
> The adapter will not start correctly unless the database is fully prepared.

---

## 2. Start the Adapter

Once the setup script completes successfully, start the adapter:

```
local-deployment\start-adapter.bat
```

---

## 3. (Optional) Connect Grafana or Other Tools

You can now connect Grafana or other tools to the database using the credentials set up in step 1.

---

**If you need to reset the database**, you may want to run the clear script or drop/recreate the database before running the setup script again.
